# LLM Project

这是一个使用 PyTorch 和 Transformers 的大语言模型项目，现在使用 Poetry 进行依赖管理。

## 环境要求

- Python 3.11+
- Poetry (用于依赖管理)

## 安装 Poetry

如果还没有安装 Poetry，可以使用以下命令安装：

```bash
curl -sSL https://install.python-poetry.org | python3 -
```

安装完成后，将 Poetry 添加到 PATH：

```bash
export PATH="$HOME/.local/bin:$PATH"
```

## 项目设置

1. 克隆项目后，进入项目目录
2. 安装依赖：

```bash
# 安装所有依赖（包括开发依赖）
poetry install

# 仅安装生产依赖
poetry install --only main

# 安装开发依赖
poetry install --with dev
```

## 使用 Poetry

### 激活虚拟环境

```bash
# 激活 Poetry 创建的虚拟环境
poetry shell

# 或者在虚拟环境中运行命令
poetry run python your_script.py
```

### 管理依赖

```bash
# 添加新的依赖
poetry add package_name

# 添加开发依赖
poetry add --group dev package_name

# 移除依赖
poetry remove package_name

# 更新依赖
poetry update

# 查看依赖树
poetry show --tree
```

### 导出依赖

如果需要生成 requirements.txt 文件：

```bash
# 导出生产依赖
poetry export -f requirements.txt --output requirements.txt --without-hashes

# 导出所有依赖（包括开发依赖）
poetry export -f requirements.txt --output requirements-dev.txt --with dev --without-hashes
```

## 项目结构

```
.
├── pyproject.toml          # Poetry 配置文件
├── poetry.lock            # 锁定的依赖版本
├── requirements.txt       # 原始的 requirements 文件（可以删除）
├── base/                  # 模型文件
├── scripts/               # 脚本文件
└── tools/                 # 工具文件
```

## 主要依赖

- **PyTorch**: 深度学习框架
- **Transformers**: Hugging Face 的 Transformers 库
- **Datasets**: 数据集处理
- **Accelerate**: 训练加速
- **DeepSpeed**: 分布式训练
- **PEFT**: 参数高效微调
- **Weights & Biases**: 实验跟踪
- **其他**: 数据处理、可视化、配置管理等工具

## 注意事项

1. Flash Attention 的 wheel 文件需要手动安装：
   ```bash
   poetry run pip install flash_attn-2.8.1+cu12torch2.6cxx11abiTRUE-cp311-cp311-linux_x86_64.whl
   ```

2. 如果遇到依赖冲突，可以尝试：
   ```bash
   poetry lock --no-update
   poetry install
   ```

3. 虚拟环境位置：项目根目录下的 `.venv` 文件夹
