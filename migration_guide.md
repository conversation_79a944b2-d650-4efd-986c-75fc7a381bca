# 从 requirements.txt 迁移到 Poetry 指南

## 迁移完成状态

✅ **迁移已完成！** 您的项目现在使用 Poetry 进行依赖管理。

## 迁移前后对比

### 之前 (requirements.txt)
```bash
pip install -r requirements.txt
```

### 现在 (Poetry)
```bash
poetry install
```

## 已安装的依赖

### 核心依赖
- **PyTorch**: 2.6.0+cu124 (深度学习框架)
- **Transformers**: 4.52.4 (Hugging Face 模型库)
- **Datasets**: 4.0.0 (数据集处理)
- **Accelerate**: 1.8.1 (训练加速)
- **NumPy**: 1.26.4 (数值计算)
- **Pandas**: 2.3.1 (数据处理)

### 训练优化
- **DeepSpeed**: 0.17.2 (分布式训练)
- **PEFT**: 0.16.0 (参数高效微调)
- **BitsAndBytes**: 0.46.1 (量化训练)

### 监控和日志
- **Weights & Biases**: 0.21.0 (实验跟踪)
- **TensorBoard**: 2.19.0 (可视化)

### 数据处理和NLP
- **NLTK**: 3.9.1 (自然语言处理)
- **Jieba**: 0.42.1 (中文分词)
- **SentencePiece**: 0.2.0 (分词器)

### 配置管理
- **Hydra-Core**: 1.3.2 (配置管理)
- **OmegaConf**: 2.3.0 (配置文件)

### 可视化
- **Matplotlib**: 3.10.3 (绘图)
- **Seaborn**: 0.13.2 (统计可视化)
- **Plotly**: 6.2.0 (交互式图表)

### 开发工具 (dev dependencies)
- **Jupyter**: 1.1.1 (交互式开发)
- **IPython**: 9.4.0 (增强的Python shell)
- **Black**: 25.1.0 (代码格式化)
- **Flake8**: 7.3.0 (代码检查)

## 常用 Poetry 命令

### 环境管理
```bash
# 激活虚拟环境
poetry shell

# 在虚拟环境中运行命令
poetry run python your_script.py

# 查看虚拟环境信息
poetry env info
```

### 依赖管理
```bash
# 安装所有依赖
poetry install

# 仅安装生产依赖
poetry install --only main

# 安装开发依赖
poetry install --with dev

# 添加新依赖
poetry add package_name

# 添加开发依赖
poetry add --group dev package_name

# 移除依赖
poetry remove package_name

# 更新依赖
poetry update

# 查看依赖树
poetry show --tree
```

### 导出依赖
```bash
# 导出为 requirements.txt
poetry export -f requirements.txt --output requirements.txt --without-hashes

# 导出包含开发依赖
poetry export -f requirements.txt --output requirements-dev.txt --with dev --without-hashes
```

## 项目结构

```
.
├── pyproject.toml          # Poetry 配置文件 (替代 requirements.txt)
├── poetry.lock            # 锁定的依赖版本 (自动生成)
├── .venv/                 # 虚拟环境 (自动创建)
├── README.md              # 项目说明
├── test_environment.py    # 环境测试脚本
├── requirements.txt       # 原始文件 (可以删除)
├── base/                  # 模型文件
├── scripts/               # 脚本文件
└── tools/                 # 工具文件
```

## 特殊依赖处理

### Flash Attention
Flash Attention 的 wheel 文件需要手动安装：
```bash
poetry run pip install flash_attn-2.8.1+cu12torch2.6cxx11abiTRUE-cp311-cp311-linux_x86_64.whl
```
注意：当前 wheel 文件与平台不兼容，可能需要重新编译或使用其他版本。

## 优势

### 相比 requirements.txt 的优势
1. **依赖解析**: 自动解决版本冲突
2. **锁定文件**: poetry.lock 确保环境一致性
3. **虚拟环境**: 自动管理虚拟环境
4. **开发依赖**: 区分生产和开发依赖
5. **项目元数据**: 统一管理项目信息
6. **构建系统**: 支持打包和发布

### 版本管理
- 使用语义化版本约束 (如 `^1.0.0`)
- 自动选择兼容版本
- 锁定文件确保可重现构建

## 测试环境

运行环境测试：
```bash
poetry run python test_environment.py
```

预期输出：
```
🚀 开始测试 Poetry 环境...
==================================================
🔍 测试依赖导入...
✅ PyTorch: 2.6.0+cu124
✅ Transformers: 4.52.4
✅ Datasets: 4.0.0
✅ Accelerate: 1.8.1
✅ NumPy: 1.26.4
✅ Pandas: 2.3.1

🔍 测试 PyTorch 功能...
✅ 张量运算正常，结果形状: torch.Size([3, 3])
✅ CUDA 可用，设备数量: 2
✅ CUDA 张量操作正常

==================================================
🎉 所有测试通过！Poetry 环境配置成功！
```

## 下一步

1. **删除旧文件**: 确认一切正常后，可以删除 `requirements.txt`
2. **团队协作**: 分享 `pyproject.toml` 和 `poetry.lock` 文件
3. **CI/CD**: 更新构建脚本使用 Poetry
4. **文档更新**: 更新项目文档中的安装说明

## 故障排除

### 常见问题
1. **依赖冲突**: 使用 `poetry lock --no-update` 重新锁定
2. **虚拟环境问题**: 删除 `.venv` 文件夹重新创建
3. **缓存问题**: 使用 `poetry cache clear pypi --all` 清理缓存

### 获取帮助
```bash
poetry --help
poetry install --help
poetry add --help
```
