[tool.poetry]
name = "llm-project"
version = "0.1.0"
description = "LLM project with PyTorch and Transformers"
authors = ["User <<EMAIL>>"]
readme = "README.md"
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.11"

# 核心依赖 - 先安装这些
torch = "2.6.0"
torchvision = "0.21.0"
transformers = ">=4.40.0,<=4.52.4"
tokenizers = ">=0.19.0"
numpy = ">=1.24.0,<2.0.0"
tqdm = ">=4.66.0"
requests = ">=2.31.0"
pyyaml = ">=6.0"

# 可选依赖 - 可以后续添加
# modelscope = {version = "*", optional = true}
# datasets = {version = ">=2.19.0", optional = true}
# accelerate = {version = ">=0.30.0", optional = true}
# deepspeed = {version = ">=0.14.0", optional = true}
# peft = {version = ">=0.10.0", optional = true}
# bitsandbytes = {version = ">=0.43.0", optional = true}
# pandas = {version = ">=2.0.0", optional = true}
# scikit-learn = {version = ">=1.3.0", optional = true}
# nltk = {version = ">=3.8.0", optional = true}
# jieba = {version = ">=0.42.1", optional = true}
# wandb = {version = ">=0.16.0", optional = true}
# tensorboard = {version = ">=2.15.0", optional = true}
# hydra-core = {version = ">=1.3.0", optional = true}
# omegaconf = {version = ">=2.3.0", optional = true}
# matplotlib = {version = ">=3.7.0", optional = true}
# seaborn = {version = ">=0.12.0", optional = true}
# plotly = {version = ">=5.17.0", optional = true}
# psutil = {version = ">=5.9.0", optional = true}
# GPUtil = {version = ">=1.4.0", optional = true}
# httpx = {version = ">=0.27.0", optional = true}
# rich = {version = ">=13.7.0", optional = true}
# typer = {version = ">=0.9.0", optional = true}
# click = {version = ">=8.1.0", optional = true}
# sentencepiece = {version = ">=0.2.0", optional = true}
# protobuf = {version = ">=4.25.0", optional = true}
datasets = "^4.0.0"
accelerate = "^1.8.1"
deepspeed = "^0.17.2"
peft = "^0.16.0"
bitsandbytes = "^0.46.1"
pandas = "^2.3.1"
scikit-learn = "^1.7.0"
nltk = "^3.9.1"
jieba = "^0.42.1"
wandb = "^0.21.0"
tensorboard = "^2.19.0"
hydra-core = "^1.3.2"
omegaconf = "^2.3.0"
matplotlib = "^3.10.3"
seaborn = "^0.13.2"
plotly = "^6.2.0"
psutil = "^7.0.0"
gputil = "^1.4.0"
httpx = "^0.28.1"
rich = "^14.0.0"
typer = "^0.16.0"
click = "^8.2.1"
sentencepiece = "^0.2.0"
protobuf = "^6.31.1"
modelscope = "^1.28.0"
wheel = "^0.45.1"

[tool.poetry.group.dev.dependencies]
# 开发工具
jupyter = ">=1.0.0"
ipython = ">=8.12.0"
black = ">=23.0.0"
flake8 = ">=6.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
