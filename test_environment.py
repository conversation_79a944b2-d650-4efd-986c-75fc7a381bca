#!/usr/bin/env python3
"""
测试 Poetry 环境中的依赖是否正常工作
"""

def test_imports():
    """测试主要依赖的导入"""
    print("🔍 测试依赖导入...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
    except ImportError as e:
        print(f"❌ PyTorch 导入失败: {e}")
        return False
    
    try:
        import transformers
        print(f"✅ Transformers: {transformers.__version__}")
    except ImportError as e:
        print(f"❌ Transformers 导入失败: {e}")
        return False
    
    try:
        import datasets
        print(f"✅ Datasets: {datasets.__version__}")
    except ImportError as e:
        print(f"❌ Datasets 导入失败: {e}")
        return False
    
    try:
        import accelerate
        print(f"✅ Accelerate: {accelerate.__version__}")
    except ImportError as e:
        print(f"❌ Accelerate 导入失败: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✅ NumPy: {np.__version__}")
    except ImportError as e:
        print(f"❌ NumPy 导入失败: {e}")
        return False
    
    try:
        import pandas as pd
        print(f"✅ Pandas: {pd.__version__}")
    except ImportError as e:
        print(f"❌ Pandas 导入失败: {e}")
        return False
    
    return True

def test_torch_functionality():
    """测试 PyTorch 基本功能"""
    print("\n🔍 测试 PyTorch 功能...")
    
    try:
        import torch
        
        # 测试基本张量操作
        x = torch.randn(3, 3)
        y = torch.randn(3, 3)
        z = torch.matmul(x, y)
        print(f"✅ 张量运算正常，结果形状: {z.shape}")
        
        # 测试 CUDA 可用性
        if torch.cuda.is_available():
            print(f"✅ CUDA 可用，设备数量: {torch.cuda.device_count()}")
            device = torch.device('cuda')
            x_cuda = x.to(device)
            print(f"✅ CUDA 张量操作正常")
        else:
            print("ℹ️  CUDA 不可用，使用 CPU")
        
        return True
    except Exception as e:
        print(f"❌ PyTorch 功能测试失败: {e}")
        return False

def test_transformers_functionality():
    """测试 Transformers 基本功能"""
    print("\n🔍 测试 Transformers 功能...")
    
    try:
        from transformers import AutoTokenizer
        
        # 测试分词器
        tokenizer = AutoTokenizer.from_pretrained("bert-base-uncased")
        text = "Hello, world!"
        tokens = tokenizer(text, return_tensors="pt")
        print(f"✅ 分词器正常，输入: '{text}'")
        print(f"✅ 分词结果: {tokens['input_ids'].shape}")
        
        return True
    except Exception as e:
        print(f"❌ Transformers 功能测试失败: {e}")
        return False

def test_flash_attention():
    """测试 Flash Attention（可选）"""
    print("\n🔍 测试 Flash Attention...")

    try:
        import flash_attn
        print(f"✅ Flash Attention 导入成功: {flash_attn.__version__}")

        from flash_attn import flash_attn_func
        print("✅ flash_attn_func 导入成功!")

        # 简单功能测试
        import torch
        if torch.cuda.is_available():
            batch_size, seq_len, num_heads, head_dim = 1, 128, 4, 32
            q = torch.randn(batch_size, seq_len, num_heads, head_dim, dtype=torch.float16, device='cuda')
            k = torch.randn(batch_size, seq_len, num_heads, head_dim, dtype=torch.float16, device='cuda')
            v = torch.randn(batch_size, seq_len, num_heads, head_dim, dtype=torch.float16, device='cuda')

            output = flash_attn_func(q, k, v, dropout_p=0.0, softmax_scale=None, causal=True)
            print(f"✅ Flash Attention 计算成功，输出形状: {output.shape}")

        return True
    except Exception as e:
        print(f"⚠️  Flash Attention 不可用: {e}")
        print("ℹ️  这是可选依赖，不影响其他功能")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试 Poetry 环境...")
    print("=" * 50)

    # 测试导入
    import_success = test_imports()

    if not import_success:
        print("\n❌ 依赖导入测试失败，请检查安装")
        return False

    # 测试 PyTorch 功能
    torch_success = test_torch_functionality()

    # 测试 Flash Attention（可选）
    flash_attn_success = test_flash_attention()

    # 测试 Transformers 功能（可选，因为需要下载模型）
    # transformers_success = test_transformers_functionality()

    print("\n" + "=" * 50)
    if import_success and torch_success:
        print("🎉 核心测试通过！Poetry 环境配置成功！")
        if flash_attn_success:
            print("🎊 Flash Attention 也可正常使用！")
        else:
            print("ℹ️  Flash Attention 不可用，但不影响其他功能")
        return True
    else:
        print("❌ 部分测试失败，请检查配置")
        return False

if __name__ == "__main__":
    main()
